C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Cast-Stone-api.exe
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Cast-Stone-api.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Cast-Stone-api.deps.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Cast-Stone-api.runtimeconfig.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Cast-Stone-api.pdb
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\BCrypt.Net-Next.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Humanizer.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Mono.TextTemplating.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Npgsql.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Composition.AttributedModel.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Composition.Convention.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Composition.Hosting.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Composition.Runtime.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\System.Composition.TypedParts.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.AssemblyInfo.cs
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.sourcelink.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\scopedcss\bundle\Cast-Stone-api.styles.css
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\refint\Cast-Stone-api.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.pdb
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.genruntimeconfig.cache
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\ref\Cast-Stone-api.dll
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\bin\Debug\net8.0\global.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets\msbuild.Cast-Stone-api.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets\msbuild.build.Cast-Stone-api.props
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.Cast-Stone-api.props
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.Cast-Stone-api.props
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Desktop\cast-stonev2\cast-stonev2\Backend\Cast-Stone-api\obj\Debug\net8.0\Cast-Stone-api.csproj.CopyComplete
